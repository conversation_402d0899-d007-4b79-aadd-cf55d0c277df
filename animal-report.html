<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Animal Report – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
  <style>
    /* Report-specific styles */
    .report-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      background: white;
      min-height: 100vh;
    }

    .report-header {
      text-align: center;
      margin-bottom: 2rem;
      border-bottom: 2px solid #2c3e50;
      padding-bottom: 1rem;
    }

    .report-title {
      color: #2c3e50;
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }

    .report-subtitle {
      color: #666;
      font-size: 1.1rem;
    }

    .animal-overview {
      display: grid;
      grid-template-columns: 200px 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
      padding: 1.5rem;
      background: #f8f9fa;
      border-radius: 10px;
    }

    .animal-photo {
      width: 200px;
      height: 200px;
      border-radius: 10px;
      object-fit: cover;
      object-position: top;
    }

    .animal-details {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }

    .detail-item {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
    }

    .detail-label {
      font-weight: 600;
      color: #2c3e50;
      font-size: 0.9rem;
    }

    .detail-value {
      color: #555;
      font-size: 1rem;
    }

    .report-section {
      margin-bottom: 2rem;
      page-break-inside: avoid;
    }

    .section-title {
      color: #2c3e50;
      font-size: 1.4rem;
      margin-bottom: 1rem;
      border-bottom: 1px solid #ddd;
      padding-bottom: 0.5rem;
    }

    .logs-table, .medical-table, .adoptions-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 1rem;
    }

    .logs-table th, .logs-table td,
    .medical-table th, .medical-table td,
    .adoptions-table th, .adoptions-table td {
      border: 1px solid #ddd;
      padding: 0.75rem;
      text-align: left;
    }

    .logs-table th, .medical-table th, .adoptions-table th {
      background: #f8f9fa;
      font-weight: 600;
      color: #2c3e50;
    }

    .floating-buttons {
      position: fixed;
      top: 20px;
      right: 20px;
      display: flex;
      gap: 10px;
      z-index: 1000;
    }

    .floating-btn {
      background: rgba(66, 133, 166, 0.9);
      color: white;
      border: none;
      border-radius: 50px;
      padding: 12px 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
      font-weight: 500;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
    }

    .floating-btn:hover {
      background: rgba(66, 133, 166, 1);
      transform: translateY(-2px);
    }

    .close-btn {
      background: rgba(66, 133, 166, 0.9);
    }

    .close-btn:hover {
      background: rgba(66, 133, 166, 1);
      transform: translateY(-2px);
    }

    .no-data {
      text-align: center;
      color: #666;
      font-style: italic;
      padding: 2rem;
    }

    /* Print styles */
    @media print {
      .floating-buttons {
        display: none !important;
      }
      
      .report-container {
        padding: 0;
        box-shadow: none;
      }
      
      .animal-overview {
        background: white;
        border: 1px solid #ddd;
      }
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
      .report-container {
        padding: 1rem;
      }
      
      .animal-overview {
        grid-template-columns: 1fr;
        text-align: center;
      }
      
      .animal-details {
        grid-template-columns: 1fr;
      }
      
      .floating-buttons {
        position: relative;
        top: auto;
        right: auto;
        justify-content: center;
        margin-bottom: 1rem;
      }
    }
  </style>
</head>
<body>
  <!-- Floating Action Buttons -->
  <div class="floating-buttons">
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">print</span>
      Print Report
    </button>
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">picture_as_pdf</span>
      Save as PDF
    </button>
    <button class="floating-btn close-btn" onclick="closeWindow()">
      <span class="material-icons">close</span>
      Close Window
    </button>
  </div>

  <div class="report-container">
    <div class="report-header">
      <h1 class="report-title">Animal Report</h1>
      <p class="report-subtitle" id="report-subtitle">Loading...</p>
    </div>

    <div class="animal-overview" id="animal-overview">
      <!-- Animal overview will be populated by JavaScript -->
    </div>

    <!-- Daily Logs Section -->
    <div class="report-section" id="logs-section" style="display: none;">
      <h2 class="section-title">Daily Care Logs</h2>
      <div id="logs-content">
        <div class="no-data">Loading daily logs...</div>
      </div>
    </div>

    <!-- Medical Episodes Section -->
    <div class="report-section" id="medical-section" style="display: none;">
      <h2 class="section-title">Medical History</h2>
      <div id="medical-content">
        <div class="no-data">Loading medical history...</div>
      </div>
    </div>

    <!-- Adoptions & Donations Section -->
    <div class="report-section" id="adoptions-section" style="display: none;">
      <h2 class="section-title">Adoption & Donation History</h2>
      <div id="adoptions-content">
        <div class="no-data">Loading adoption history...</div>
      </div>
    </div>
  </div>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const animalId = urlParams.get('animalId');
    const animalName = urlParams.get('animalName');
    const sections = urlParams.get('sections')?.split(',') || [];
    const fullHistory = urlParams.get('fullHistory') === 'true';
    const startDate = urlParams.get('startDate');
    const endDate = urlParams.get('endDate');

    // Initialize report
    async function initializeReport() {
      if (!animalId) {
        document.querySelector('.report-container').innerHTML = '<div class="no-data">No animal selected for report.</div>';
        return;
      }

      // Update report subtitle
      const dateRange = fullHistory ? 'Full History' : `${startDate} to ${endDate}`;
      document.getElementById('report-subtitle').textContent = `${animalName} - ${dateRange}`;

      // Load animal data
      await loadAnimalOverview();

      // Load selected sections
      if (sections.includes('logs')) {
        document.getElementById('logs-section').style.display = 'block';
        await loadDailyLogs();
      }

      if (sections.includes('medical')) {
        document.getElementById('medical-section').style.display = 'block';
        await loadMedicalHistory();
      }

      if (sections.includes('adoptions')) {
        document.getElementById('adoptions-section').style.display = 'block';
        await loadAdoptionHistory();
      }
    }

    // Load animal overview
    async function loadAnimalOverview() {
      const { data, error } = await supabase
        .from('animals')
        .select('*')
        .eq('id', animalId)
        .single();

      if (error || !data) {
        document.getElementById('animal-overview').innerHTML = '<div class="no-data">Animal data not found.</div>';
        return;
      }

      document.getElementById('animal-overview').innerHTML = `
        <img src="${data.photo_url || 'assets/images/placeholder.jpg'}" alt="${data.name}" class="animal-photo" />
        <div class="animal-details">
          <div class="detail-item">
            <span class="detail-label">Name</span>
            <span class="detail-value">${data.name || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Species</span>
            <span class="detail-value">${data.species || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Group</span>
            <span class="detail-value">${data.Group || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Age</span>
            <span class="detail-value">${data.Age || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Status</span>
            <span class="detail-value">${data.status || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Notes</span>
            <span class="detail-value">${data.notes || 'No notes available'}</span>
          </div>
        </div>
      `;
    }

    // Load daily logs
    async function loadDailyLogs() {
      let query = supabase
        .from('daily_logs')
        .select('*')
        .eq('animal_id', animalId)
        .order('created_on', { ascending: false });

      if (!fullHistory && startDate && endDate) {
        query = query.gte('created_on', startDate).lte('created_on', endDate);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Daily logs error:', error);
        document.getElementById('logs-content').innerHTML = '<div class="no-data">Error loading daily logs.</div>';
        return;
      }

      generateDailyLogsTable(data, false);
    }

    function generateDailyLogsTable(data, hasStaffInfo) {
      if (!data || data.length === 0) {
        document.getElementById('logs-content').innerHTML = '<div class="no-data">No daily logs found for this period.</div>';
        return;
      }

      const tableHTML = `
        <table class="logs-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Fed</th>
              <th>Cleaned</th>
              <th>Medication</th>
              <th>Notes</th>
              <th>Logged By</th>
            </tr>
          </thead>
          <tbody>
            ${data.map(log => `
              <tr>
                <td>${new Date(log.created_on).toLocaleDateString()}</td>
                <td>${log.fed || 'N/A'}</td>
                <td>${log.cleaned || 'N/A'}</td>
                <td>${log.medication || 'N/A'}</td>
                <td>${log.notes || 'No notes'}</td>
                <td>${log.logged_by || 'N/A'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      `;

      document.getElementById('logs-content').innerHTML = tableHTML;
    }

    // Load medical history
    async function loadMedicalHistory() {
      let query = supabase
        .from('medical_episodes')
        .select(`
          *,
          medical_interventions (*)
        `)
        .eq('animal_id', animalId)
        .order('created_at', { ascending: false });

      if (!fullHistory && startDate && endDate) {
        query = query.gte('created_at', startDate).lte('created_at', endDate);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Medical history error:', error);
        document.getElementById('medical-content').innerHTML = '<div class="no-data">Error loading medical history.</div>';
        return;
      }

      if (!data || data.length === 0) {
        document.getElementById('medical-content').innerHTML = '<div class="no-data">No medical episodes found for this period.</div>';
        return;
      }

      let medicalHTML = '';
      data.forEach(episode => {
        medicalHTML += `
          <div style="margin-bottom: 2rem; padding: 1rem; border: 1px solid #ddd; border-radius: 8px;">
            <h4 style="color: #2c3e50; margin-bottom: 0.5rem;">${episode.title}</h4>
            <p><strong>Type:</strong> ${episode.type}</p>
            <p><strong>Severity:</strong> ${episode.severity}</p>
            <p><strong>Status:</strong> ${episode.status}</p>
            <p><strong>Date Discovered:</strong> ${new Date(episode.date_discovered).toLocaleDateString()}</p>
            <p><strong>Description:</strong> ${episode.description || 'No description'}</p>
            <p><strong>Created:</strong> ${new Date(episode.created_at).toLocaleDateString()}</p>

            ${episode.medical_interventions && episode.medical_interventions.length > 0 ? `
              <h5 style="margin-top: 1rem; margin-bottom: 0.5rem;">Interventions:</h5>
              <table class="medical-table" style="margin-top: 0.5rem;">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Type</th>
                    <th>Description</th>
                    <th>Staff Member</th>
                    <th>Outcome</th>
                    <th>Next Steps</th>
                  </tr>
                </thead>
                <tbody>
                  ${episode.medical_interventions.map(intervention => {
                    return `
                      <tr>
                        <td>${new Date(intervention.intervention_date).toLocaleDateString()}</td>
                        <td>${intervention.intervention_type || 'N/A'}</td>
                        <td>${intervention.description || 'No description'}</td>
                        <td>${intervention.staff_member || 'Unknown Staff'}</td>
                        <td>${intervention.outcome || 'N/A'}</td>
                        <td>${intervention.next_steps || 'N/A'}</td>
                      </tr>
                    `;
                  }).join('')}
                </tbody>
              </table>
            ` : '<p><em>No interventions recorded for this episode.</em></p>'}
          </div>
        `;
      });

      document.getElementById('medical-content').innerHTML = medicalHTML;
    }

    // Load adoption history
    async function loadAdoptionHistory() {
      let query = supabase
        .from('adoptions_donations')
        .select('*')
        .eq('animal_id', animalId)
        .order('created_at', { ascending: false });

      if (!fullHistory && startDate && endDate) {
        query = query.gte('created_at', startDate).lte('created_at', endDate);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Adoption history error:', error);
        document.getElementById('adoptions-content').innerHTML = '<div class="no-data">Error loading adoption history.</div>';
        return;
      }

      if (!data || data.length === 0) {
        document.getElementById('adoptions-content').innerHTML = '<div class="no-data">No adoption or donation records found for this period.</div>';
        return;
      }

      const tableHTML = `
        <table class="adoptions-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Type</th>
              <th>Donor Name</th>
              <th>Email</th>
              <th>Amount</th>
              <th>Start Date</th>
              <th>End Date</th>
              <th>Notes</th>
            </tr>
          </thead>
          <tbody>
            ${data.map(record => {
              return `
                <tr>
                  <td>${new Date(record.created_at).toLocaleDateString()}</td>
                  <td>${record.donation_type}</td>
                  <td>${record.donor_name || 'Not specified'}</td>
                  <td>${record.donor_email || 'Not provided'}</td>
                  <td>£${parseFloat(record.amount || 0).toFixed(2)}</td>
                  <td>${record.start_date ? new Date(record.start_date).toLocaleDateString() : 'N/A'}</td>
                  <td>${record.end_date ? new Date(record.end_date).toLocaleDateString() : 'N/A'}</td>
                  <td>${record.notes || 'No notes'}</td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      `;

      document.getElementById('adoptions-content').innerHTML = tableHTML;
    }

    // Initialize the report
    initializeReport();

    // Close window function - make it globally accessible
    function closeWindow() {
      window.location.href = 'reports.html';
    }

    // Make closeWindow globally accessible
    window.closeWindow = closeWindow;
  </script>
</body>
</html>
