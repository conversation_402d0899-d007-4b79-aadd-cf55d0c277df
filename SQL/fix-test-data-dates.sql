-- Fix Test Data Dates for Chart Testing
-- This script updates the created_at timestamps of adoption/donation records
-- to be spread across the last 6 months for proper chart visualization

-- Update records with random dates from the last 6 months
WITH random_dates AS (
  SELECT 
    id,
    -- Generate random dates between 6 months ago and today
    (CURRENT_DATE - INTERVAL '6 months') + 
    (RANDOM() * (CURRENT_DATE - (CURRENT_DATE - INTERVAL '6 months'))) AS new_date
  FROM adoptions_donations
  ORDER BY id
)
UPDATE adoptions_donations 
SET created_at = rd.new_date + (RANDOM() * INTERVAL '23 hours 59 minutes')
FROM random_dates rd
WHERE adoptions_donations.id = rd.id;

-- Alternative approach: Update specific records to specific months
-- Uncomment the section below if you prefer more controlled distribution

/*
-- Update first 20 records to be from 6 months ago
UPDATE adoptions_donations 
SET created_at = CURRENT_DATE - INTERVAL '6 months' + (RANDOM() * INTERVAL '30 days')
WHERE id IN (
  SELECT id FROM adoptions_donations ORDER BY id LIMIT 20
);

-- Update next 20 records to be from 5 months ago  
UPDATE adoptions_donations 
SET created_at = CURRENT_DATE - INTERVAL '5 months' + (RANDOM() * INTERVAL '30 days')
WHERE id IN (
  SELECT id FROM adoptions_donations ORDER BY id OFFSET 20 LIMIT 20
);

-- Update next 20 records to be from 4 months ago
UPDATE adoptions_donations 
SET created_at = CURRENT_DATE - INTERVAL '4 months' + (RANDOM() * INTERVAL '30 days')
WHERE id IN (
  SELECT id FROM adoptions_donations ORDER BY id OFFSET 40 LIMIT 20
);

-- Update next 20 records to be from 3 months ago
UPDATE adoptions_donations 
SET created_at = CURRENT_DATE - INTERVAL '3 months' + (RANDOM() * INTERVAL '30 days')
WHERE id IN (
  SELECT id FROM adoptions_donations ORDER BY id OFFSET 60 LIMIT 20
);

-- Update next 20 records to be from 2 months ago
UPDATE adoptions_donations 
SET created_at = CURRENT_DATE - INTERVAL '2 months' + (RANDOM() * INTERVAL '30 days')
WHERE id IN (
  SELECT id FROM adoptions_donations ORDER BY id OFFSET 80 LIMIT 20
);

-- Leave the last 20 records in the current month (July)
*/

-- Verify the distribution
SELECT 
  DATE_TRUNC('month', created_at) as month,
  donation_type,
  COUNT(*) as count
FROM adoptions_donations 
GROUP BY DATE_TRUNC('month', created_at), donation_type
ORDER BY month, donation_type;
