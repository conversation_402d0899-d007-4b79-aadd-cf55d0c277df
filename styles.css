/* General Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 1rem;
  background: #4285a6;
  background: linear-gradient(47deg, rgb(127, 186, 215) 0%, rgb(162, 210, 163) 59%, rgb(253, 246, 181) 100%);
  background-attachment: fixed;
  min-height: 100vh;
  color: #333;
  position: relative;
}

/* Add subtle grey shading to both sides for curved background effect */
body::before,
body::after {
  content: '';
  position: fixed;
  top: 0;
  bottom: 0;
  width: 15%;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.03), transparent);
  pointer-events: none;
  z-index: -1;
}

body::before {
  left: 0;
}

body::after {
  right: 0;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.03), transparent);
}

/* Main container with frosted glass effect - Mobile First */
.app-container {
  width: 95%;
  max-width: 1400px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  min-height: calc(100vh - 2rem);
}

/* Desktop adjustments */
@media (min-width: 768px) {
  body {
    padding: 2rem;
  }

  .app-container {
    width: 90%;
    padding: 2rem;
    border-radius: 30px;
    min-height: calc(100vh - 4rem);
  }
}

img {
  max-width: 100%;
  height: 100%;
  display: block;
  object-position: top;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

h1 {
  font-size: 2rem;
  color: #333;
}

h2 {
  font-size: 1.5rem;
  color: #444;
}

h3 {
  font-size: 1.25rem;
  color: #555;
}

p {
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* Header - Mobile First */
.top-bar {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(15px);
  color: #2c3e50;
  padding: 1rem;
  border-radius: 15px;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1rem;
  font-weight: 600;
  position: relative;
}

.top-bar h1 {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.1rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 120px);
  margin: 0;
}

/* Desktop header adjustments */
@media (min-width: 768px) {
  .top-bar {
    padding: 1.5rem 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    font-size: 1.25rem;
  }

  .top-bar h1 {
    font-size: 1.5rem;
    max-width: calc(100% - 160px);
  }
}

.menu-btn {
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  padding: 0.75rem;
  font-size: 1.2rem;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-weight: 600;
}

.menu-btn:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.7);
}

/* Dashboard */
.dashboard {
  padding: 0;
}

.search-bar {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  padding: 0.6rem 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: #2c3e50;
  width: 100%;
  transition: all 0.3s ease;
  height: 44px;
  box-sizing: border-box;
}

.search-bar:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.8);
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.search-bar::placeholder {
  color: rgba(44, 62, 80, 0.6);
}

/* Controls Container */
.controls-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* Filter Dropdowns */
.filter-dropdowns {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.filter-dropdown {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  padding: 0.6rem 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  height: 44px;
  box-sizing: border-box;
}

.filter-dropdown:hover {
  background: rgba(255, 255, 255, 0.7);
  border-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.filter-dropdown:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.8);
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* Animal Cards - Mobile First with 2 Column Grid */
.animal-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-top: 0;
}

.animal-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 25px;
  padding: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  text-align: left;
  min-height: auto;
  max-width: 100%;
  overflow: hidden;
  word-wrap: break-word;
}

.animal-card:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.7);
  border-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Top section with photo, name, and status badge */
.animal-card .top-section {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.animal-card .photo-container {
  flex-shrink: 0;
  position: relative;
}

.animal-card img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  object-position: top;
  border: 3px solid rgba(255, 255, 255, 0.8);
}

.animal-card .name-status-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 0;
  overflow: hidden;
}

.animal-card .name {
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  line-height: 1.2;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.animal-card .details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.animal-card .detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  font-size: clamp(0.8rem, 2vw, 0.9rem);
  gap: 0.5rem;
  min-height: 1.2em;
}

.animal-card .detail-label {
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
  white-space: nowrap;
}

.animal-card .detail-value {
  color: #2c3e50;
  font-weight: 600;
  text-align: right;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  min-width: 0;
}

.animal-card .status-badge {
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  align-self: flex-start;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: 100%;
  display: inline-block;
  text-align: center;
  line-height: 1.2;
}

/* Status Badge Colors Based on Status */
.animal-card .status-badge {
  background: #4CAF50; /* Default green for "All Clear" */
}

/* All Clear - Green */
.animal-card .status-badge[data-status="all clear"],
.animal-card .status-badge[data-status="healthy"],
.animal-card .status-badge[data-status="released"],
.animal-card .status-badge[data-status="recovered"] {
  background: #4CAF50;
}

/* Under Observation - Light Green */
.animal-card .status-badge[data-status="under observation"] {
  background: #8BC34A;
}

/* Unwell - Yellow */
.animal-card .status-badge[data-status="unwell"] {
  background: #CDDC39;
  color: #333;
}

/* Vet Booked - Orange */
.animal-card .status-badge[data-status="vet booked"] {
  background: #FF9800;
}

/* In Treatment - Orange-Red */
.animal-card .status-badge[data-status="in treatment"] {
  background: #FF5722;
}

/* Recovery - Blue */
.animal-card .status-badge[data-status="recovery"] {
  background: #2196F3;
}

/* Ongoing Condition - Purple */
.animal-card .status-badge[data-status="ongoing condition"] {
  background: #9C27B0;
}

/* Palliative - Dark Purple */
.animal-card .status-badge[data-status="palliative"] {
  background: #673AB7;
}

/* Quarantined - Red */
.animal-card .status-badge[data-status="quarantined"] {
  background: #df3427;
}

/* Transferred - Pink */
.animal-card .status-badge[data-status="transferred"] {
  background: #ff3679;
}

/* Deceased - Black */
.animal-card .status-badge[data-status="deceased"] {
  background: #424242;
}

.animal-card .details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.animal-card .detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.animal-card .detail-label {
  font-weight: 600;
  color: #34495e;
  font-size: 0.9rem;
}

.animal-card .detail-value {
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: right;
}

/* Animals Section Headings */
.animals-section-heading {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 2rem 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(66, 133, 166, 0.3);
  grid-column: 1 / -1; /* Span full width of grid */
}

.animals-section-heading.archived-heading {
  color: #6c757d;
  border-bottom-color: rgba(108, 117, 125, 0.3);
}

/* Archived Animal Cards */
.animal-card.archived {
  opacity: 0.7;
  background: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.6);
  position: relative;
}

.animal-card.archived:hover {
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px); /* Less hover effect than active cards */
}

.animal-card.archived .name,
.animal-card.archived .detail-label,
.animal-card.archived .detail-value {
  color: #6c757d;
}

/* Archived Badge */
.archived-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #6c757d;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 2;
}

/* Keep archived cards clickable for viewing/editing */
.animal-card.archived {
  cursor: pointer;
}

/* Medium tablet adjustments (768px - 1020px) */
@media (min-width: 768px) and (max-width: 1020px) {
  .controls-container {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-dropdowns {
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  .filter-dropdown {
    flex: 1;
    max-width: 180px;
    min-width: 140px;
  }

  .search-bar {
    max-width: 100%;
  }

  .animal-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  /* Tablet-specific animal card adjustments */
  .animal-card {
    padding: 1rem;
    gap: 0.6rem;
  }

  .animal-card .top-section {
    gap: 0.8rem;
  }

  .animal-card img {
    width: 70px;
    height: 70px;
  }

  .animal-card .name {
    font-size: clamp(0.95rem, 2.2vw, 1.1rem);
  }

  .animal-card .status-badge {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
    letter-spacing: 0.2px;
    line-height: 1.2;
  }

  .animal-card .detail-row {
    font-size: clamp(0.75rem, 1.8vw, 0.85rem);
  }
}

/* Large desktop adjustments */
@media (min-width: 1021px) {
  .controls-container {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
  }

  .filter-dropdowns {
    gap: 1.5rem;
  }

  .filter-dropdown {
    padding: 0.7rem 1.2rem;
    font-size: 1rem;
    min-width: 160px;
  }

  .search-bar {
    max-width: 300px;
  }

  .animal-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  /* Desktop-specific animal card adjustments */
  .animal-card {
    padding: 1.2rem;
    gap: 0.75rem;
  }

  .animal-card .top-section {
    gap: 1rem;
  }

  .animal-card img {
    width: 80px;
    height: 80px;
  }

  .animal-card .name {
    font-size: clamp(1.1rem, 1.5vw, 1.2rem);
  }

  .animal-card .status-badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    letter-spacing: 0.3px;
    line-height: 1.2;
  }

  .animal-card .detail-row {
    font-size: clamp(0.85rem, 1.2vw, 0.9rem);
  }
}

  .animal-card {
    border-radius: 30px;
    padding: 2rem;
    gap: 1.5rem;
  }

  .animal-card img {
    width: 140px;
    height: 140px;
  }

  .animal-card .name {
    font-size: 1.6rem;
  }

  .animal-card .status-badge {
    padding: 0.5rem 1.2rem;
    font-size: 0.9rem;
  }

  .animal-card .detail-label {
    font-size: 1rem;
  }

  .animal-card .detail-value {
    font-size: 1rem;
    text-align: right;
  }

/* Add Animal Modal */
#new_animal-modal {
  display: none;
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
  z-index: 999;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  box-sizing: border-box;
  overflow-y: auto;
}

.new_animal-input .modal-content {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 1.5rem;
  border-radius: 25px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  width: 95%;
  max-width: 800px;
  max-height: 90vh;
  position: relative;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.new_animal-input .close-modal {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  cursor: pointer;
  color: #2c3e50;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-weight: 600;
}

.new_animal-input .close-modal:hover {
  background: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.1);
}

/* Modal form content styling for proper scrolling */
.new_animal-input .form-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
  margin-right: -0.5rem;
}

.new_animal-input h2 {
  flex-shrink: 0;
  margin-bottom: 1.5rem;
  padding-right: 2rem; /* Account for close button */
}

.new_animal-input .submit-btn {
  flex-shrink: 0;
  margin-top: 1rem;
}

/* Add Form Page */
.form-wrapper {
  max-width: 650px;
  margin: 2rem auto;
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.form-wrapper form label {
  display: block;
  margin: 1rem 0 0.5rem;
  font-weight: bold;
}

form label {
  display: block;
  margin-top: 0.5rem;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

input[type="text"],
input[type="url"],
textarea,
select {
  width: 100%;
  margin-top: 0.5rem;
  padding: 1rem 1.25rem;
  border-radius: 15px;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  font-size: 1rem;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

input[type="text"]:focus,
input[type="url"]:focus,
textarea:focus,
select:focus {
  outline: none;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.95);
}

/* Modern Button Styles */
button[type="submit"],
.fab,
.floating-button,
.print-button,
#toggle-logs {
  margin-top: 1.5rem;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(15px);
  color: #2c3e50;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 15px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

button[type="submit"]:hover,
.print-button:hover,
.fab:hover,
.floating-button:hover,
#toggle-logs:hover {
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 0.4);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.6);
}

/* Floating Button */
.fab {
  position: fixed;
  bottom: 90px; /* Position above GDPR footer */
  right: 30px;
  background: #4285a6;
  backdrop-filter: blur(15px);
  color: white;
  border: 1px solid #4285a6;
  padding: 1rem 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.fab:hover {
  background: #3a7594;
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

/* Floating Action Button Section */
.floating-actions {
  position: fixed;
  bottom: 90px; /* Position above GDPR footer */
  right: 30px;
  z-index: 998;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Animal Detail Page - Mobile First */
.animal-detail {
  width: 95%;
  max-width: 650px;
  margin: 1rem auto;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.animal-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 1.5rem;
}

.animal-header h1 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.animal-detail img {
  width: 180px;
  height: 180px;
  object-fit: cover;
  border-radius: 12px;
  margin-bottom: 1rem;
}

.animal-info {
  text-align: left;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Mobile-first info layout - labels above values */
.info-row {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  line-height: 1.4;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.info-row strong {
  font-weight: bold;
  color: #2c3e50;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-row span {
  font-size: 1rem;
  color: #333;
  white-space: pre-line;
}

/* Desktop adjustments */
@media (min-width: 768px) {
  .animal-detail {
    width: 90%;
    margin: 2rem auto;
    padding: 2.5rem;
    border-radius: 25px;
  }

  .animal-header h1 {
    font-size: 2rem;
  }

  .animal-detail img {
    width: 200px;
    height: 200px;
  }

  .animal-info {
    max-width: 500px;
    gap: 0.5rem;
  }

  /* Desktop info layout - labels beside values */
  .info-row {
    flex-direction: row;
    gap: 0.5rem;
    align-items: flex-start;
    padding: 0.5rem;
    background: transparent;
    border: none;
  }

  .info-row strong {
    min-width: 80px;
    font-size: 1rem;
    text-transform: none;
    letter-spacing: normal;
  }
}

.qr-container {
  margin: 2rem auto 1rem;
}

/* Custom Checkbox Styles */
.custom-checkbox {
  display: inline-flex;
  flex-direction: row;
  gap: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
}

.custom-checkbox input[type="checkbox"] {
  display: none;
}

.custom-checkbox .checkmark {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background-color: #f44336; /* Red */
  border: 1px solid #ccc;
  display: inline-block;
  position: relative;
  transition: all 0.2s ease;
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark {
  background-color: #4CAF50; /* Green */
  border: 1px solid #4CAF50;
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '✅';
  text-align: center;
  color: white;
  font-size: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Log Entries */
.log-entry {
  text-align: left;
  background: #f9f9f9;
  padding: 0.75rem;
  border: 1px solid #ddd;
  margin-top: 0.5rem;
  border-radius: 6px;
}

/* Modal Styles - Mobile First */
.modal,
#log-modal,
#edit-animal-modal {
  display: none;
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: rgba(0,0,0,0.6);
  backdrop-filter: blur(5px);
  z-index: 999;
  justify-content: center;
  align-items: flex-start;
  padding: 1rem;
  overflow-y: auto;
}

.modal-content {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  padding: 1.5rem;
  border-radius: 15px;
  width: 100%;
  max-width: 500px;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-top: 2rem;
  max-height: calc(100vh - 4rem);
  overflow-y: auto;
}

/* Desktop modal adjustments */
@media (min-width: 768px) {
  .modal,
  #log-modal,
  #edit-animal-modal {
    align-items: center;
    padding: 2rem;
  }

  .modal-content {
    width: 80%;
    padding: 2rem;
    border-radius: 10px;
    margin-top: 0;
    max-height: 90vh;
  }
}

/* Form styling to match frosted glass theme - Apply to all forms */
.form-content,
.form-group,
form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-content label,
.form-group label,
form label {
  display: flex;
  flex-direction: column;
  font-weight: bold;
  color: #333;
  gap: 0.5rem;
}

/* Override existing input styles with frosted glass theme */
.form-content input,
.form-content select,
.form-content textarea,
.form-group input,
.form-group select,
.form-group textarea,
.modal-content input,
.modal-content select,
.modal-content textarea,
form input[type="text"],
form input[type="url"],
form input[type="date"],
form input[type="file"],
form textarea,
form select {
  padding: 0.75rem !important;
  border-radius: 8px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  background: rgba(255, 255, 255, 0.4) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  font-size: 1rem !important;
  transition: all 0.3s ease !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.form-content input:focus,
.form-content select:focus,
.form-content textarea:focus,
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus,
.modal-content input:focus,
.modal-content select:focus,
.modal-content textarea:focus,
form input[type="text"]:focus,
form input[type="url"]:focus,
form input[type="date"]:focus,
form textarea:focus,
form select:focus {
  outline: none !important;
  border-color: rgba(66, 133, 166, 0.6) !important;
  background: rgba(255, 255, 255, 0.6) !important;
  box-shadow: 0 4px 12px rgba(66, 133, 166, 0.2) !important;
  transform: translateY(-2px) !important;
}

.photo-tab {
  background: rgba(255, 255, 255, 0.3);
  color: #666;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.photo-tab:first-child {
  border-radius: 4px 0 0 4px;
}

.photo-tab:last-child {
  border-radius: 0 4px 4px 0;
}

.photo-tab.active-tab {
  background: rgba(66, 133, 166, 0.7);
  color: white;
  border-color: rgba(66, 133, 166, 0.8);
}

/* Universal button styling for forms */
.cancel-btn,
form button[type="cancel"],
.submit-btn,
form button[type="submit"],
.modal-content button[type="submit"],
.form-group button[type="submit"] {
  padding: 0.75rem 1.5rem !important;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(15px);
  color: #2c3e50;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 50px;
  cursor: pointer !important;
  font-weight: bold !important;
  font-size: 1rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease !important;
  margin-top: 1rem !important;
  width: fit-content !important;
}

.submit-btn:hover,
form button[type="submit"]:hover,
.modal-content button[type="submit"]:hover,
.form-group button[type="submit"]:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
}

.remove-photo-btn {
  display: block;
  margin-top: 0.5rem;
  background: rgba(220, 53, 69, 0.8);
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.remove-photo-btn:hover {
  background: rgba(220, 53, 69, 1);
}

.modal-content h2 {
  margin-top: 0;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.close-modal {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 1.5rem;
  color: #333;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1000;
}

.close-modal:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

/* Print Overrides */
@media print {
  .print-button,
  .floating-button,
  .floating-actions,
  #log-feedback,
  #recent-logs-section,
  #log-modal,
  #edit-animal-modal {
    display: none !important;
  }
}

/* Log Display */
.log-table {
  width: 95%;
  border-collapse: collapse;
  border: 2px solid #2e6b2e;
  border-radius: 10px;
  overflow: hidden;
}

.log-table th,
.log-table td {
  padding: 0.75rem;
  text-align: left;
  vertical-align: top;
  border: 1px solid #2e6b2e;
}

.log-table th {
  width: 120px;
  font-weight: bold;
  background: #f8f8f8;
}

/* Header Styling */
.top-bar .title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-weight: bold;
}

/* Old menu-btn styles removed - using enhanced version above */

.back-link {
  color: #2c3e50;
  text-decoration: none;
  font-size: 1.5rem;
  font-weight: 600;
  padding: 0.5rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  min-height: 40px;
}

.back-link:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* Old side-menu styles removed - using enhanced version below */

/* Floating Action Menu Styles */
.fab-container {
  position: fixed;
  bottom: 80px; /* Position above GDPR footer */
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
}

.fab-secondary {
  background: #4285a6;
  color: white;
  border: 1px solid #4285a6;
}

.fab-secondary:hover {
  background: #3a7594;
  border-color: #3a7594;
}

.fab-main {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #4285a6;
  color: white;
  border: none;
  border-radius: 32px;
  padding: 0.75rem 1rem;
  font-size: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.fab-main .material-icons {
  font-size: 24px;
  color: white;
  transition: transform 0.3s ease;
}

.fab-main:hover {
  background-color: #3a7594;
  transform: scale(1.05);
}

.fab-main.active {
  background-color: #3a7594;
}

.fab-main.active .material-icons {
  transform: rotate(45deg);
}

/* FAB actions styling moved to dashboard section - see line 5640+ */

/* FAB action styling moved to dashboard section - see line 5659+ */

/* Print Styles - Hide interactive elements when printing */
@media print {
  .fab-container,
  .fab-main,
  .fab-actions,
  .fab-action,
  .menu-btn,
  .back-link,
  .modal,
  button:not(.no-print) {
    display: none !important;
  }

  /* Ensure the main content prints well */
  body {
    background: white;
    color: black;
  }

  .animal-detail {
    margin: 0;
    padding: 20px;
  }

  .top-bar {
    background: white !important;
    color: black !important;
    border-bottom: 2px solid #000;
    box-shadow: none;
  }

  .top-bar .title {
    color: black !important;
  }

  /* Make sure animal info prints clearly */
  .animal-info {
    page-break-inside: avoid;
  }

  /* Ensure logs section prints well */
  .logs-section {
    page-break-inside: avoid;
  }

  /* Hide any interactive buttons in logs */
  .logs-section button {
    display: none !important;
  }
}

/* Medical Tracker Styles */
.medical-tracker {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  padding-top: 2rem;
}

.animal-summary {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.animal-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.animal-photo-small {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #446c35;
}

.animal-basic-info h1 {
  margin: 0 0 0.5rem 0;
  color: #446c35;
  font-size: 1.8rem;
}

.animal-basic-info p {
  margin: 0.25rem 0;
  color: #666;
}

.medical-overview {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 2.5rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.section-header h2 {
  margin: 0;
  color: #446c35;
  font-size: 1.5rem;
}

.episodes-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.episode-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: none;
  border-radius: 20px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.episode-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
}

.episode-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.episode-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.episode-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  font-size: 0.95rem;
  color: #666;
  align-items: center;
  flex-wrap: wrap;
}

.episode-status {
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.episode-status.active {
  background: #ffebee;
  color: #c62828;
  margin-left: 5px;
}

.episode-status.monitoring {
  background: #fff3e0;
  color: #ef6c00;
  margin-left: 5px;
}

.episode-status.resolved {
  background: #e8f5e8;
  color: #2e7d32;
  margin-left: 5px;
}

.severity-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.severity-badge.low {
  background: #e8f5e8;
  color: #2e7d32;
  margin-left: 5px;
}

.severity-badge.medium {
  background: #fff3e0;
  color: #ef6c00;
  margin-left: 5px;
}

.severity-badge.high {
  background: #ffebee;
  color: #c62828;
  margin-left: 5px;
}

.severity-badge.critical {
  background: #3f1a1a;
  color: white;
  margin-left: 5px;
}

.episode-description {
  color: #555;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.episode-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #888;
}

/* Episode Detail Styles */
.episode-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.episode-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.episode-detail-title {
  font-size: 1.5rem;
  color: #446c35;
  margin: 0 0 0.5rem 0;
}

.interventions-section {
  margin-top: 2rem;
}

.interventions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.interventions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.intervention-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  background: #f9f9f9;
}

.intervention-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.intervention-type {
  background: #446c35;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.intervention-date {
  font-size: 0.9rem;
  color: #666;
}

.intervention-description {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.intervention-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.intervention-detail {
  background: white;
  padding: 0.5rem;
  border-radius: 4px;
  border-left: 3px solid #446c35;
}

.intervention-detail strong {
  display: block;
  color: #446c35;
  margin-bottom: 0.25rem;
}

/* Modal Enhancements - Mobile First */
.modal-content.large {
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  margin-top: 1rem;
}

/* Desktop large modal adjustments */
@media (min-width: 768px) {
  .modal-content.large {
    width: 90%;
    margin-top: 0;
  }
}

/* Form Enhancements */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #446c35;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #446c35;
  box-shadow: 0 0 0 2px rgba(68, 108, 53, 0.1);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.btn-primary {
  background: #446c35;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background 0.2s ease;
}

.btn-primary:hover {
  background: #5a8a47;
}

.btn-secondary {
  background: white;
  color: #446c35;
  border: 2px solid #446c35;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #446c35;
  color: white;
}

.feedback {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 4px;
  font-weight: bold;
}

.feedback.success {
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #4caf50;
}

.feedback.error {
  background: #ffebee;
  color: #c62828;
  border: 1px solid #f44336;
}

/* No episodes message */
.no-episodes {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
  background: #f9f9f9;
  border-radius: 8px;
  border: 2px dashed #ddd;
}

/* Enhanced Mobile responsiveness for medical tracker */
@media (max-width: 768px) {
  .medical-tracker {
    padding: 0.75rem;
    padding-top: 1rem;
  }

  .medical-overview {
    padding: 1.5rem;
    border-radius: 20px;
  }

  .animal-header {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .animal-photo-small {
    width: 60px;
    height: 60px;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .episode-card {
    padding: 1.5rem;
    border-radius: 15px;
  }

  .episode-meta {
    flex-direction: column;
    gap: 0.8rem;
    align-items: flex-start;
  }

  .episode-meta span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .episode-detail-header {
    flex-direction: column;
    gap: 1rem;
  }

  .interventions-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .intervention-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .intervention-details {
    grid-template-columns: 1fr;
  }

  .modal-content.large {
    width: 98%;
    max-width: none;
    margin: 0.5rem;
    padding: 1rem;
    border-radius: 15px;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    justify-content: center;
  }
}

/* Print styles for medical tracker */
@media print {
  .medical-tracker {
    padding-top: 0;
  }

  .episode-card {
    page-break-inside: avoid;
    border: 1px solid #000 !important;
    margin-bottom: 1rem;
  }

  .intervention-card {
    page-break-inside: avoid;
    border: 1px solid #666 !important;
    margin-bottom: 0.5rem;
  }

  .btn-primary,
  .modal {
    display: none !important;
  }
}

/* Enhanced Mobile Responsive Styles */
@media (max-width: 767px) {
  body {
    padding: 0.5rem;
  }

  .app-container {
    width: 98%;
    padding: 0.75rem;
    border-radius: 15px;
  }

  /* Ensure mobile grid layout */
  .animal-cards {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.75rem;
  }

  /* Mobile controls */
  .controls-container {
    flex-direction: column !important;
    gap: 0.75rem;
  }

  .filter-dropdowns {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-dropdown {
    width: 100%;
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .top-bar {
    padding: 0.75rem;
    font-size: 1rem;
    border-radius: 12px;
    margin-bottom: 1rem;
  }

  .top-bar h1 {
    font-size: 0.95rem;
    max-width: calc(100% - 100px);
  }

  .search-bar {
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    width: 100%;
  }

  /* Force mobile layout on small screens */
  .animal-card {
    padding: 0.75rem;
    gap: 0.5rem;
  }

  .animal-card .top-section {
    gap: 0.75rem;
  }

  .animal-card img {
    width: 60px;
    height: 60px;
  }

  .animal-card .name {
    font-size: 1rem !important;
    margin: 0 !important;
    line-height: 1.2;
  }

  .animal-card .status-badge {
    padding: 0.3rem 0.5rem !important;
    font-size: 0.65rem !important;
    border-radius: 15px !important;
    letter-spacing: 0.2px !important;
    white-space: normal !important;
    max-width: 100% !important;
    line-height: 1.1 !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
  }

  .animal-card .details {
    gap: 0.4rem;
    margin-top: 0.25rem;
  }

  .animal-card .detail-row {
    gap: 0.25rem;
  }

  .animal-card .detail-label {
    font-size: clamp(0.7rem, 2.5vw, 0.8rem) !important;
    white-space: nowrap !important;
  }

  .animal-card .detail-value {
    font-size: clamp(0.7rem, 2.5vw, 0.8rem) !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
  }

  .animal-card h3 {
    font-size: 1rem;
    margin: 0.5rem 0;
  }

  .animal-card p {
    font-size: 0.85rem;
    margin: 0.25rem 0;
  }

  /* Mobile modal adjustments */
  .new_animal-input .modal-content,
  #edit-animal-modal .modal-content {
    width: 98%;
    max-height: 95vh;
    max-width: none;
    padding: 1rem;
    margin: 0.5rem;
    border-radius: 12px;
    overflow-y: auto;
  }

  .new_animal-input .form-content {
    max-height: none;
    overflow-y: visible;
    padding-right: 0;
    margin-right: 0;
  }

  .new_animal-input h2 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    padding-right: 2.5rem;
  }

  .new_animal-input .close-modal,
  #edit-animal-modal .close-modal {
    top: 8px;
    right: 12px;
    width: 28px;
    height: 28px;
    font-size: 18px;
  }

  /* Mobile form improvements */
  .new_animal-input label,
  #edit-animal-modal label {
    margin-bottom: 1rem;
  }

  .new_animal-input input,
  .new_animal-input select,
  .new_animal-input textarea,
  #edit-animal-modal input,
  #edit-animal-modal select,
  #edit-animal-modal textarea {
    padding: 0.75rem;
    font-size: 1rem;
    border-radius: 8px;
  }

  .new_animal-input .submit-btn {
    padding: 1rem;
    font-size: 1rem;
    margin-top: 1.5rem;
  }

  /* Edit modal specific mobile adjustments */
  #edit-animal-modal form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  #edit-animal-modal label {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    font-weight: bold;
    margin-bottom: 0;
  }

  #edit-animal-modal input,
  #edit-animal-modal select,
  #edit-animal-modal textarea {
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.4);
    font-size: 1rem;
  }

  #edit-animal-modal button[type="submit"] {
    margin-top: 1rem;
    padding: 1rem;
    font-size: 1rem;
    border-radius: 8px;
  }

  /* Photo preview adjustments for mobile */
  #edit-photo-preview img,
  #current-photo-image {
    max-width: 150px !important;
    max-height: 150px !important;
  }

  /* Medical tracker modal adjustments for mobile */
  #new-episode-modal .modal-content,
  #new-intervention-modal .modal-content,
  #update-status-modal .modal-content {
    width: 98%;
    max-width: none;
    margin: 0.5rem;
    padding: 1rem;
    border-radius: 12px;
    max-height: 90vh;
    overflow-y: auto;
  }

  #new-episode-modal .close-modal,
  #new-intervention-modal .close-modal,
  #update-status-modal .close-modal,
  #episode-detail-modal .close-modal {
    top: 8px;
    right: 12px;
    width: 28px;
    height: 28px;
    font-size: 18px;
  }

  /* Episode detail modal mobile adjustments */
  #episode-detail-modal .modal-content {
    width: 98%;
    max-width: none;
    margin: 0.5rem;
    padding: 1rem;
    border-radius: 12px;
    max-height: 90vh;
    overflow-y: auto;
  }

  /* Form adjustments for medical tracker modals */
  .modal .form-group {
    margin-bottom: 1rem;
  }

  .modal .form-group label {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .modal .form-group input,
  .modal .form-group select,
  .modal .form-group textarea {
    padding: 0.75rem;
    font-size: 1rem;
    border-radius: 8px;
  }

  .modal .form-actions {
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }

  .modal .btn-primary,
  .modal .btn-secondary {
    width: 100%;
    justify-content: center;
    padding: 1rem;
    font-size: 1rem;
  }

  /* Floating button adjustments */
  .fab {
    bottom: 70px; /* Position above GDPR footer on mobile */
    right: 20px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    border-radius: 25px;
    background: #4285a6;
    color: white;
  }

  .fab-container {
    bottom: 70px; /* Position above GDPR footer on mobile */
  }

  .floating-actions {
    bottom: 70px; /* Position above GDPR footer on mobile */
  }

  /* Form adjustments */
  .form-wrapper {
    margin: 1rem auto;
    padding: 1.5rem;
    border-radius: 20px;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid rgba(66, 133, 166, 0.5);
  outline-offset: 2px;
}

/* Document Management Styles */
.documents-main {
  padding: 2rem 0;
  max-width: 1200px;
  margin: 0 auto;
}

/* Upload Section */
.upload-section {
  margin-bottom: 3rem;
}

.upload-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.upload-card h2 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.upload-area {
  border: 2px dashed rgba(66, 133, 166, 0.4);
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 1.5rem;
}

.upload-area.drag-over {
  border-color: rgba(66, 133, 166, 0.8);
  background: rgba(66, 133, 166, 0.1);
  transform: scale(1.02);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  font-size: 3rem !important;
  color: rgba(66, 133, 166, 0.7);
}

.upload-area p {
  margin: 0;
  color: #2c3e50;
}

.file-types {
  font-size: 0.9rem;
  color: #666;
}

.upload-btn {
  background: linear-gradient(135deg, rgba(66, 133, 166, 0.8), rgba(87, 199, 89, 0.8));
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(66, 133, 166, 0.3);
}

.category-selection {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.category-selection label {
  font-weight: 600;
  color: #2c3e50;
}

.upload-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, rgba(66, 133, 166, 0.8), rgba(87, 199, 89, 0.8));
  border-radius: 4px;
  transition: width 0.3s ease;
  width: 0%;
}

#progress-text {
  font-size: 0.9rem;
  color: #2c3e50;
  font-weight: 600;
}

/* Documents Section */
.documents-section {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.documents-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.documents-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.view-toggle {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  color: #2c3e50;
}

.view-btn:hover,
.view-btn.active {
  background: rgba(66, 133, 166, 0.7);
  color: white;
  border-color: rgba(66, 133, 166, 0.8);
}

.category-filters {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-btn {
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  color: #2c3e50;
  font-size: 0.9rem;
  white-space: normal;
  word-wrap: break-word;
  text-align: center;
  line-height: 1.3;
}

.filter-btn:hover,
.filter-btn.active {
  background: rgba(66, 133, 166, 0.7);
  color: white;
  border-color: rgba(66, 133, 166, 0.8);
}

/* Documents Grid View - Mobile First */
.documents-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.document-card {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.3s ease;
  position: relative;
  width: 100%;
}

.document-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.6);
}

/* Tablet and Desktop adjustments */
@media (min-width: 480px) {
  .documents-grid {
    gap: 1.25rem;
  }
}

@media (min-width: 768px) {
  .documents-grid {
    gap: 1.5rem;
  }

  .document-card {
    padding: 1.5rem;
  }

  .document-card:hover {
    transform: translateY(-4px);
  }
}

.document-category {
  margin: 0 0 0.5rem 0;
  font-size: 0.85rem;
  color: rgba(66, 133, 166, 0.8);
  font-weight: 500;
  text-align: left;
}

.document-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.document-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.document-icon {
  flex-shrink: 0;
}

.document-icon .material-icons {
  font-size: 2rem !important;
  color: rgba(66, 133, 166, 0.8);
}

.document-info {
  flex: 1;
  text-align: left;
}

.document-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
}

.document-right {
  text-align: right;
  flex-shrink: 0;
}

.document-meta {
  margin: 0;
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
}

.document-meta .upload-date {
  display: block;
  margin-bottom: 0.25rem;
}

.document-meta .file-size {
  display: block;
}

.document-actions {
  display: flex;
  justify-content: flex-start;
  gap: 0.5rem;
}

.action-btn {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  color: #2c3e50;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.download-btn:hover {
  background: rgba(87, 199, 89, 0.7);
  color: white;
  border-color: rgba(87, 199, 89, 0.8);
}

.delete-btn:hover {
  background: rgba(220, 53, 69, 0.7);
  color: white;
  border-color: rgba(220, 53, 69, 0.8);
}

/* Documents List View */
.documents-list {
  display: block;
}

.documents-table {
  width: 100%;
}

.table-header,
.table-row {
  display: grid;
  grid-template-columns: 40px 1fr 150px 80px 100px 100px;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.table-header {
  background: rgba(66, 133, 166, 0.1);
  font-weight: 600;
  color: #2c3e50;
  border: 1px solid rgba(66, 133, 166, 0.2);
}

.table-row {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.table-row:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: translateX(4px);
}

.col-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.col-actions {
  display: flex;
  gap: 0.25rem;
  justify-content: flex-end;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #666;
}

.empty-state .material-icons {
  font-size: 4rem !important;
  color: rgba(66, 133, 166, 0.3);
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.empty-state p {
  margin: 0;
  color: #666;
}

/* Side Menu Enhancements */
.side-menu {
  position: fixed;
  top: 0;
  left: -250px;
  width: 250px;
  height: 100vh;
  background: rgba(44, 62, 80, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  transition: left 0.3s ease;
  z-index: 1000;
  padding: 0;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
}

.side-menu.open {
  left: 0;
}

.menu-header {
  padding: 2rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-header h2,
.menu-header h3 {
  margin: 0;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-menu-btn,
.close-menu {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-menu-btn:hover,
.close-menu:hover {
  background: rgba(255, 255, 255, 0.1);
}

.close-menu .material-icons {
  font-size: 1.2rem;
}

.side-menu ul,
.menu-items {
  list-style: none;
  padding: 1rem 0;
  margin: 0;
}

.side-menu li {
  margin: 0;
}

.menu-items li {
  margin: 0;
}

.side-menu a {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.side-menu a .material-icons {
  font-size: 1.2rem;
  min-width: 1.2rem;
}

.side-menu a:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-left-color: rgba(87, 199, 89, 0.8);
}

.side-menu a.active,
.side-menu li.active a {
  background: rgba(66, 133, 166, 0.2);
  color: white;
  border-left-color: rgba(66, 133, 166, 0.8);
}

.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Delete Modal Enhancements */
.delete-btn {
  background: rgba(220, 53, 69, 0.8) !important;
  color: white !important;
  border-color: rgba(220, 53, 69, 0.9) !important;
}

.delete-btn:hover {
  background: rgba(220, 53, 69, 1) !important;
  transform: translateY(-2px) !important;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

/* Enhanced Responsive Design for Documents */
@media (max-width: 768px) {
  .documents-main {
    padding: 0.75rem;
  }

  .upload-card,
  .documents-section {
    padding: 1.25rem;
    border-radius: 12px;
  }

  .upload-area {
    padding: 1.5rem 1rem;
    border-radius: 10px;
  }

  .upload-content {
    gap: 0.75rem;
  }

  .upload-icon {
    font-size: 2.5rem !important;
  }

  .documents-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .category-filters {
    justify-content: center;
    gap: 0.5rem;
  }

  .filter-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    white-space: normal;
    word-wrap: break-word;
    text-align: center;
    line-height: 1.3;
  }

  .table-header,
  .table-row {
    grid-template-columns: 30px 1fr 60px 80px;
    gap: 0.5rem;
    font-size: 0.9rem;
    padding: 0.75rem;
  }

  .col-category,
  .col-date {
    display: none;
  }

  .col-actions {
    justify-content: center;
  }

  .document-meta {
    flex-direction: column;
    gap: 0.25rem;
    align-items: center;
    font-size: 0.75rem;
  }

  .document-title {
    font-size: 0.9rem;
  }

  .document-category {
    font-size: 0.8rem;
  }
}

/* Staff & Volunteers Module Styles */
.staff-volunteers-main {
  padding: 0;
}

.search-filter-section {
  margin-bottom: 2rem;
}

.filter-section {
  margin-top: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: flex-end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 150px;
}

.filter-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.filter-select {
  padding: 0.75rem 1rem;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  font-size: 0.9rem;
  color: #2c3e50;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(66, 133, 166, 0.5);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.filter-btn {
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
  height: fit-content;
  margin-top: 0.5rem;
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(66, 133, 166, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.filter-btn .material-icons {
  font-size: 1rem;
}

/* Staff & Volunteers Grid - Mobile First */
.staff-volunteers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  margin-top: 0;
}

/* Person Cards */
.person-card {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.person-card:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Compact Card Layout */
.person-card.compact {
  padding: 1rem;
}

.person-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.person-photo {
  flex-shrink: 0;
}

.person-basic-info {
  flex: 1;
  text-align: left;
}

.person-basic-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: #2c3e50;
}

/* Legacy styles for detail modal */
.person-info {
  text-align: center;
}

.person-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  color: #2c3e50;
}

/* Role Badges - Rounded Pill Style */
.role-badge {
  display: inline-block;
  padding: 0.4rem 1rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0;
  white-space: nowrap;
}

.staff-badge {
  background: rgba(66, 133, 166, 0.2);
  color: rgba(66, 133, 166, 0.9);
  border: 1px solid rgba(66, 133, 166, 0.3);
}

.volunteer-badge {
  background: rgba(87, 199, 89, 0.2);
  color: rgba(87, 199, 89, 0.9);
  border: 1px solid rgba(87, 199, 89, 0.3);
}

/* Person Details */
.person-details {
  text-align: left;
  margin-bottom: 1rem;
}

.person-details .info-row {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.person-details .info-row strong {
  font-size: 0.85rem;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.person-details .info-row span {
  font-size: 0.95rem;
  color: #333;
}

/* Compact Person Details */
.person-compact-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.compact-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.4rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 0.9rem;
}

.compact-info-row:last-child {
  border-bottom: none;
}

.compact-info-row strong {
  color: #2c3e50;
  font-weight: 600;
  min-width: 80px;
}

.compact-info-row span {
  color: #333;
  text-align: right;
  }

/* Status Styling */
.status-active {
  color: #2e7d32 !important;
  font-weight: bold;
  border-radius: 20px;
  padding: 5px;
}

.status-inactive {
  background: rgba(255, 255, 255, 0.401);
  border: 1px solid #d32f2f !important;
  color: #d32f2f !important;
  font-weight: bold;
  border-radius: 20px;
  padding: 5px;
}

/* Person Detail Button */
.person-detail-btn {
  width: 100%;
  margin-top: 1rem;
}

/* Person Detail Modal */
.person-detail-info .info-row {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.person-detail-info .info-row strong {
  font-size: 0.9rem;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.person-detail-info .info-row span {
  font-size: 1rem;
  color: #333;
}

.person-detail-info .info-row a {
  color: rgba(66, 133, 166, 0.8);
  text-decoration: none;
}

.person-detail-info .info-row a:hover {
  color: rgba(66, 133, 166, 1);
  text-decoration: underline;
}

/* GDPR Footer */
.gdpr-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(44, 62, 80, 0.95);
  backdrop-filter: blur(20px);
  padding: 0.75rem;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 100;
}

.gdpr-footer a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.gdpr-footer a:hover {
  color: white;
  text-decoration: underline;
}

/* GDPR Modal Content */
.gdpr-content {
  text-align: left;
  line-height: 1.6;
}

.gdpr-content h3 {
  margin: 1.5rem 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.gdpr-content ul {
  margin: 0.5rem 0 1rem 1.5rem;
}

.gdpr-content li {
  margin-bottom: 0.25rem;
}

/* Responsive Design for Staff & Volunteers */
@media (min-width: 480px) {
  .staff-volunteers-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (min-width: 768px) {
  .staff-volunteers-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .person-card {
    border-radius: 20px;
  }

  .person-card.compact {
    padding: 1.25rem;
  }

  /* Desktop info layout - labels beside values */
  .person-details .info-row,
  .person-detail-info .info-row {
    flex-direction: row;
    gap: 0.5rem;
    align-items: flex-start;
    padding: 0.5rem;
    background: transparent;
    border: none;
  }

  .person-details .info-row strong,
  .person-detail-info .info-row strong {
    min-width: 80px;
    font-size: 1rem;
    text-transform: none;
    letter-spacing: normal;
  }

  .person-detail-btn {
    width: auto;
    padding: 0.75rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .staff-volunteers-main {
    padding: 0.5rem;
  }

  .staff-volunteers-grid {
    grid-template-columns: 1fr;
  }

  .person-card {
    border-radius: 12px;
  }

  .person-card.compact {
    padding: 0.75rem;
  }

  .person-basic-info h3 {
    font-size: 1rem;
  }

  .role-badge {
    font-size: 0.75rem;
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
  }

  .compact-info-row {
    font-size: 0.85rem;
  }

  .person-details .info-row strong {
    font-size: 0.8rem;
  }

  .person-details .info-row span {
    font-size: 0.9rem;
  }

  /* GDPR footer adjustments for mobile */
  .gdpr-footer {
    padding: 0.5rem;
  }

  .gdpr-footer a {
    font-size: 0.8rem;
  }

  /* Adjust body padding to account for GDPR footer */
  body {
    padding-bottom: 3rem;
  }
}

/* Donations & Adoptions Module Styles */
.donations-adoptions-main {
  padding: 0;
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.summary-card {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.summary-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(66, 133, 166, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.summary-icon .material-icons {
  color: rgba(66, 133, 166, 0.8);
  font-size: 1.5rem;
}

.summary-info h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

/* Table Section */
.table-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  margin-bottom: 2rem;
}

.table-wrapper {
  overflow-x: auto;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.data-table th {
  background: rgba(66, 133, 166, 0.1);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid rgba(66, 133, 166, 0.2);
  white-space: nowrap;
}

.data-table td {
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  vertical-align: top;
}

.data-table tr:hover {
  background: rgba(66, 133, 166, 0.05);
}

/* Table Cell Styles */
.animal-info strong,
.donor-info strong {
  display: block;
  color: #2c3e50;
  font-weight: 600;
}

.animal-info small,
.donor-info small {
  display: block;
  color: #666;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.amount-cell {
  font-weight: 600;
  color: #2e7d32;
  text-align: right;
}

/* Badge Styles - Rounded Pill Style */
.type-badge,
.status-badge {
  display: inline-block;
  padding: 0.4rem 1rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  min-width: 80px;
  white-space: nowrap;
}

.type-adoption {
  background: rgba(87, 199, 89, 0.2);
  color: rgba(87, 199, 89, 0.9);
  border: 1px solid rgba(87, 199, 89, 0.3);
}

.type-donation {
  background: rgba(66, 133, 166, 0.2);
  color: rgba(66, 133, 166, 0.9);
  border: 1px solid rgba(66, 133, 166, 0.3);
}

.status-active {
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #2e7d32;
}

.status-expiring {
  background: #fff3e0;
  color: #ef6c00;
  border: 1px solid #ef6c00;
}

.status-expired {
  background: #ffebee;
  color: #c62828;
  border: 1px solid #c62828;
}

/* Button Styles */
.btn-icon {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background: rgba(66, 133, 166, 0.1);
  border-color: rgba(66, 133, 166, 0.3);
  transform: translateY(-1px);
}

.btn-icon .material-icons {
  font-size: 1.2rem;
  color: #666;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(66, 133, 166, 0.2);
  border-top: 4px solid rgba(66, 133, 166, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Toast Notification */
.toast {
  position: fixed;
  top: 20%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1.5rem 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  max-width: 350px;
  min-width: 250px;
  text-align: center;
  font-weight: 600;
  font-size: 1rem;
}

.toast.show {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
  visibility: visible;
}

.toast.success {
  border-left: 4px solid #2e7d32;
  color: #2e7d32;
}

.toast.success::before {
  content: "✅ ";
  margin-right: 0.5rem;
}

.toast.error {
  border-left: 4px solid #c62828;
  color: #c62828;
}

.toast.error::before {
  content: "❌ ";
  margin-right: 0.5rem;
}

/* Mobile Responsive Styles for Donations & Adoptions */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .filter-group {
    min-width: auto;
  }

  .filter-btn {
    align-self: flex-start;
    margin-top: 0;
  }

  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .summary-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .summary-icon {
    width: 40px;
    height: 40px;
  }

  .summary-info h3 {
    font-size: 1.25rem;
  }

  .summary-info p {
    font-size: 0.8rem;
  }

  .table-section {
    padding: 1rem;
    border-radius: 15px;
  }

  .data-table th,
  .data-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.9rem;
  }

  .data-table th {
    font-size: 0.8rem;
  }

  /* Stack table cells on mobile */
  .animal-info,
  .donor-info {
    min-width: 120px;
  }

  .type-badge,
  .status-badge {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
    min-width: 70px;
  }

  .amount-cell {
    font-size: 0.9rem;
  }

  .btn-icon {
    padding: 0.4rem;
  }

  .btn-icon .material-icons {
    font-size: 1rem;
  }

  .toast {
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .donations-adoptions-main {
    padding: 0.5rem;
  }

  .search-filter-section {
    margin-bottom: 1.5rem;
  }

  .filter-section {
    gap: 0.75rem;
  }

  .filter-group {
    width: 100%;
  }

  .filter-select {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
  }

  .filter-btn {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
    width: 100%;
    justify-content: center;
  }

  .summary-cards {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .summary-card {
    padding: 0.75rem;
  }

  .table-section {
    padding: 0.75rem;
  }

  /* Make table more mobile-friendly */
  .table-wrapper {
    border-radius: 10px;
  }

  .data-table {
    font-size: 0.8rem;
  }

  .data-table th,
  .data-table td {
    padding: 0.5rem 0.25rem;
  }

  /* Hide less important columns on very small screens */
  .data-table th:nth-child(5),
  .data-table td:nth-child(5),
  .data-table th:nth-child(6),
  .data-table td:nth-child(6) {
    display: none;
  }

  .animal-info strong,
  .donor-info strong {
    font-size: 0.85rem;
  }

  .animal-info small,
  .donor-info small {
    font-size: 0.75rem;
  }

  .type-badge,
  .status-badge {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
    min-width: 60px;
  }
}

/* Authentication & Login Styles */
.login-main {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 2rem);
  padding: 2rem 0;
}

.login-container {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.login-header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

/* Auth Form Container */
.auth-form-container {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  margin-bottom: 1rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.auth-form h2 {
  text-align: center;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.auth-form p {
  text-align: center;
  color: #666;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

/* Form Help Text */
.form-help {
  display: block;
  font-size: 0.85rem;
  color: #666;
  margin-top: 0.25rem;
  line-height: 1.4;
}

.modal-description {
  color: #666;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Password Input Container */
.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input-container input {
  padding-right: 3rem !important;
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.password-toggle:hover {
  background: rgba(255, 255, 255, 0.5);
  color: #2c3e50;
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: #2c3e50;
}

.checkbox-container input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.forgot-password-link {
  color: rgba(66, 133, 166, 0.8);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.forgot-password-link:hover {
  color: rgba(66, 133, 166, 1);
  text-decoration: underline;
}

/* Auth Buttons */
.auth-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
  width: 100%;
}

.auth-btn .material-icons {
  font-size: 1.2rem;
}

/* Biometric Section */
.biometric-section {
  margin-top: 1rem;
}

.divider {
  text-align: center;
  margin: 1rem 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.3);
}

.divider span {
  background: rgba(255, 255, 255, 0.6);
  padding: 0 1rem;
  color: #666;
  font-size: 0.9rem;
}

/* Password Requirements */
.password-requirements {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
  margin-top: 0.5rem;
  display: block;
}

/* Admin Actions */
.admin-actions {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.admin-actions h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
}

.admin-actions button {
  margin: 0.5rem;
  min-width: 200px;
}

/* Mobile Responsive Styles for Authentication */
@media (max-width: 768px) {
  .login-main {
    padding: 1rem 0;
    min-height: calc(100vh - 1rem);
  }

  .login-container {
    max-width: 95%;
  }

  .auth-form-container {
    padding: 1.5rem;
    border-radius: 15px;
  }

  .login-header h1 {
    font-size: 1.5rem;
  }

  .login-header p {
    font-size: 1rem;
  }

  .auth-form h2 {
    font-size: 1.3rem;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .checkbox-container {
    justify-content: center;
  }

  .forgot-password-link {
    text-align: center;
  }

  .admin-actions button {
    min-width: auto;
    width: 100%;
    margin: 0.25rem 0;
  }
}

@media (max-width: 480px) {
  .auth-form-container {
    padding: 1rem;
    border-radius: 12px;
  }

  .auth-btn {
    padding: 0.875rem 1rem;
    font-size: 0.9rem;
  }

  .password-requirements {
    font-size: 0.75rem;
  }
}

/* User Display in Top Bar */
.user-display {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

.user-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: #2c3e50;
}

.user-role {
  font-size: 0.75rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.logout-btn .material-icons {
  font-size: 1.2rem;
  color: #2c3e50;
}

/* Error Message Styling */
.error-message {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  color: #d32f2f;
  font-weight: 500;
  margin: 2rem 0;
}

/* No Results Message Styling */
.no-results {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  color: #666;
  font-weight: 500;
  margin: 2rem 0;
  font-size: 1.1rem;
}

/* Mobile adjustments for user display */
@media (max-width: 768px) {
  .user-display {
    gap: 0.5rem;
  }

  .user-info {
    display: none; /* Hide user info on mobile to save space */
  }

  .logout-btn {
    padding: 0.4rem;
  }

  .logout-btn .material-icons {
    font-size: 1rem;
  }
}

/* ===== REPORTS MODULE ===== */

/* Report Type Buttons */
.report-type-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
  justify-content: center;
}

.report-type-btn {
  padding: 0.75rem 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.9rem;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.report-type-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.8);
}

.report-type-btn.active {
  background: rgba(66, 133, 166, 0.8);
  color: white;
  border-color: rgba(66, 133, 166, 0.9);
}

.report-type-btn .material-icons {
  font-size: 1.1rem;
}

/* Report Builder Sections */
.report-builder {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.search-section {
  margin-bottom: 1.5rem;
}

.search-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.search-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: rgba(66, 133, 166, 0.5);
  background: rgba(255, 255, 255, 0.8);
}

.animal-results {
  margin-top: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.report-animal-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.report-animal-card:hover {
  background: rgba(255, 255, 255, 0.4);
}

.report-animal-card.selected {
  background: rgba(66, 133, 166, 0.3);
  color: #2c3e50;
}

.report-animal-card img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  object-position: top;
}

.report-options {
  margin-bottom: 1.5rem;
}

.report-options h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
  font-size: 1.1rem;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: #2c3e50;
  position: relative;
}

.checkbox-label input[type="checkbox"] {
  opacity: 0;
  position: absolute;
  width: 0;
  height: 0;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(66, 133, 166, 0.5);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked ~ .checkmark {
  background: rgba(66, 133, 166, 0.8);
  border-color: rgba(66, 133, 166, 0.9);
}

.checkbox-label input[type="checkbox"]:checked ~ .checkmark::after {
  content: '✓';
  color: white;
  font-size: 14px;
  font-weight: bold;
}

/* Remember Me Checkbox Specific Styling */
.remember-me-label {
  display: flex;
  flex-direction: row !important;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: #2c3e50;
  position: relative;
  font-weight: normal !important;
}

.remember-me-label input[type="checkbox"] {
  opacity: 0;
  position: absolute;
  width: 0;
  height: 0;
}

.remember-me-checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(66, 133, 166, 0.5);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.remember-me-label input[type="checkbox"]:checked ~ .remember-me-checkmark {
  background: rgba(66, 133, 166, 0.8);
  border-color: rgba(66, 133, 166, 0.9);
}

.remember-me-label input[type="checkbox"]:checked ~ .remember-me-checkmark::after {
  content: '✓';
  color: white;
  font-size: 14px;
  font-weight: bold;
}

/* Date Range Sections */
.date-range-section {
  margin-bottom: 1.5rem;
}

.date-range-section h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
  font-size: 1.1rem;
}

.date-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.date-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.date-input {
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.date-input:focus {
  outline: none;
  border-color: rgba(66, 133, 166, 0.5);
  background: rgba(255, 255, 255, 0.8);
}

/* Filter Sections */
.filter-section {
  margin-bottom: 1.5rem;
}

.filter-section h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
  font-size: 1.1rem;
}

.filter-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.animal-filter-section {
  margin-bottom: 1.5rem;
}

.animal-filter-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

/* Reports Mobile Responsive */
@media (max-width: 768px) {
  .report-type-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .report-type-btn {
    justify-content: center;
    padding: 1rem;
  }

  .date-inputs {
    grid-template-columns: 1fr;
  }

  .filter-buttons {
    flex-direction: column;
  }

  .checkbox-group {
    gap: 1rem;
  }
}

/* ===== EMERGENCY CONTACTS MODULE ===== */

/* Emergency Action Bar */
.emergency-action-bar {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 1rem;
}

.emergency-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.emergency-btn.call-999 {
  background: linear-gradient(135deg, #d32f2f, #f44336);
  color: white;
  box-shadow: 0 4px 15px rgba(211, 47, 47, 0.3);
}

.emergency-btn.call-999:hover {
  background: linear-gradient(135deg, #b71c1c, #d32f2f);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4);
}

.emergency-btn.notify-owner {
  background: linear-gradient(135deg, #f57c00, #ff9800);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 124, 0, 0.3);
}

.emergency-btn.notify-owner:hover {
  background: linear-gradient(135deg, #e65100, #f57c00);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 124, 0, 0.4);
}

/* Emergency Contacts Main */
.emergency-contacts-main {
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Search and Filter Section */
.search-filter-section {
  margin-bottom: 2rem;
}

.search-container {
  margin-bottom: 1rem;
}

.search-input {
  width: 100%;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(66, 133, 166, 0.5);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.filter-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.filter-btn{
  padding: 0.75rem 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.9rem;
}

.filter-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.filter-btn.active {
  background: rgba(66, 133, 166, 0.8);
  color: white;
  border-color: rgba(66, 133, 166, 0.9);
}

.filter-btn.priority-high {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.filter-btn.priority-medium {
  background: #ffc107;
  color: white;
  border-color: #ffc107;
}

.filter-btn.priority-low {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.filter-btn.priority-high:hover,
.filter-btn.priority-medium:hover,
.filter-btn.priority-low:hover {
  opacity: 0.9;
}

/* Priority Sections */
.priority-section {
  margin-bottom: 2rem;
}

.priority-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 0 0.5rem;
}

.priority-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  padding: 0.5rem 1rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.priority-title.priority-high {
  background: rgba(255, 255, 255, 0.5);
  color: #dc3545;
  border: 1px solid #dc3545;
}

.priority-title.priority-medium {
  background: rgba(255, 255, 255, 0.5);
  color: #ffc107;
  border: 1px solid #ffc107;
}

.priority-title.priority-low {
  background: rgba(255, 255, 255, 0.5);
  color: #28a745;
  border: 1px solid #28a745;
}

.contact-count {
  font-size: 0.9rem;
  color: #666;
  background: rgba(255, 255, 255, 0.6);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

/* Contacts Grid */
.contacts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
}

/* Contact Cards */
.contact-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.contact-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.contact-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 0;
  cursor: pointer;
  padding-bottom: 1rem;
  transition: all 0.3s ease;
}

.contact-header:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin: -0.5rem;
  padding: 0.5rem 0.5rem 1.5rem 0.5rem;
}

.contact-info h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.contact-role {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
  font-weight: 500;
}

.priority-pill {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: none;
}

.priority-pill.priority-high {
  background: #dc3545;
  color: white;
}

.priority-pill.priority-medium {
  background: #ffc107;
  color: white;
}

.priority-pill.priority-low {
  background: #28a745;
  color: white;
}

/* Contact Details */
.contact-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.contact-expandable {
  transition: all 0.3s ease;
}

.contact-card.expanded {
  background: rgba(255, 255, 255, 0.8);
}

.contact-detail {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.contact-detail .material-icons {
  font-size: 1.1rem;
  color: #666;
  min-width: 1.1rem;
}

.contact-link {
  color: #4285f4;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.contact-link:hover {
  color: #1a73e8;
  text-decoration: underline;
}

.contact-link.primary {
  font-weight: 600;
  font-size: 1.05rem;
}

.contact-link.alt-phone {
  color: #34a853;
}

.contact-link.alt-phone:hover {
  color: #137333;
}

/* Contact Notes */
.contact-notes {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.contact-notes .material-icons {
  font-size: 1rem;
  color: #666;
  margin-right: 0.5rem;
  vertical-align: top;
  margin-top: 0.1rem;
}

.contact-notes p {
  margin: 0;
  color: #555;
  font-size: 0.9rem;
  line-height: 1.4;
  display: inline;
}

/* Contact Actions */
.contact-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.action-btn {
  padding: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn.edit-btn {
  color: #4285f4;
}

.action-btn.edit-btn:hover {
  background: rgba(66, 133, 244, 0.1);
  border-color: #4285f4;
}

.action-btn.delete-btn {
  color: #d32f2f;
}

.action-btn.delete-btn:hover {
  background: rgba(211, 47, 47, 0.1);
  border-color: #d32f2f;
}

/* Emergency FAB */
.emergency-fab {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: linear-gradient(135deg, #d32f2f, #f44336);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(211, 47, 47, 0.4);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 100;
}

.emergency-fab:hover {
  background: linear-gradient(135deg, #b71c1c, #f64040);
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(211, 47, 47, 0.5);
}

.emergency-fab .material-icons {
  font-size: 1.2rem;
}

/* Mobile Responsive Styles for Emergency Contacts */
@media (max-width: 768px) {
  .emergency-action-bar {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .emergency-btn {
    padding: 0.875rem;
    font-size: 0.9rem;
  }

  .emergency-contacts-main {
    padding: 0.75rem;
  }

  .contacts-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .contact-card {
    padding: 1.25rem;
  }

  .contact-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .priority-pill {
    align-self: flex-end;
    margin-top: -0.5rem;
  }

  .contact-info h4 {
    font-size: 1.1rem;
  }

  .emergency-fab {
    bottom: 1.5rem;
    right: 1.5rem;
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
  }

  .filter-buttons {
    justify-content: center;
  }

  .filter-btn {
    min-width: 0;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .emergency-action-bar {
    padding: 0.5rem;
    gap: 0.5rem;
    flex-direction: column;
  }

  .emergency-btn {
    padding: 1rem;
    font-size: 1rem;
  }

  .emergency-contacts-main {
    padding: 0.5rem;
  }

  .contact-card {
    padding: 1rem;
  }

  .contact-details {
    gap: 0.5rem;
  }

  .contact-detail {
    gap: 0.5rem;
  }

  .contact-link {
    font-size: 0.9rem;
  }

  .contact-link.primary {
    font-size: 1rem;
  }

  .emergency-fab {
    bottom: 1rem;
    right: 1rem;
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
    border-radius: 40px;
  }

  .emergency-fab .material-icons {
    font-size: 1rem;
  }

  .priority-title {
    font-size: 1.1rem;
  }

  .contact-count {
    font-size: 0.8rem;
    padding: 0.2rem 0.6rem;
  }

  .filter-buttons {
    grid-template-columns: repeat(2, 1fr);
    display: grid;
    gap: 0.5rem;
  }

  .filter-btn {
    padding: 0.6rem 0.75rem;
    font-size: 0.85rem;
  }

  .search-input {
    padding: 0.875rem 0.875rem 0.875rem 2.5rem;
    font-size: 0.9rem;
  }

  .search-icon {
    left: 0.75rem;
    font-size: 1.1rem;
  }
}

/* Report Generation Modal Styles */
.report-sections {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1.5rem 0;
}

.section-toggle {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.btn-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.btn-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-toggle.active {
  background: #4CAF50;
  border-color: #4CAF50;
  color: white;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-toggle.active:hover {
  background: #45a049;
  border-color: #45a049;
}

.btn-toggle .material-icons {
  font-size: 1.2rem;
}

.section-toggle small {
  color: rgba(44, 62, 80, 0.7);
  font-size: 0.85rem;
  margin-left: 0.5rem;
}

.report-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-primary, .btn-secondary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #4CAF50;
  color: white;
}

.btn-primary:hover {
  background: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.3);
  color: #2c3e50;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Mobile responsive adjustments for report modal */
@media (max-width: 768px) {
  .report-actions {
    flex-direction: column;
  }

  .btn-toggle {
    padding: 0.875rem 1rem;
    font-size: 0.9rem;
  }

  .btn-toggle .material-icons {
    font-size: 1.1rem;
  }
}

/* Report Styles */
.animal-results {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
  padding: 10px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.report-animal-card {
  background: white;
  border: 2px solid #ddd;
  border-radius: 8px;
  padding: 10px;
  width: 150px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: border 0.3s;
}

.report-animal-card img {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 8px;
}

.report-animal-card:hover {
  border-color: rgba(66, 133, 166, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.report-animal-card.selected {
  border: 3px solid rgba(66, 133, 166, 0.8);
  background: rgba(66, 133, 166, 0.05);
  box-shadow: 0 4px 16px rgba(66, 133, 166, 0.3);
  transform: translateY(-2px);
}

.animal-card.selected {
  border: 2px solid #28a745;
}

/* User Attribution Styles */
.user-attribution {
  background: rgba(66, 133, 166, 0.1);
  border: 1px solid rgba(66, 133, 166, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  margin: 1rem 0;
}

.user-attribution label {
  font-weight: 600;
  color: rgba(66, 133, 166, 0.9);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  display: block;
}

.user-attribution .readonly-field {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(66, 133, 166, 0.3);
  border-radius: 6px;
  padding: 0.5rem;
  font-size: 0.9rem;
  color: #555;
  cursor: not-allowed;
  width: 100%;
}

.user-attribution .readonly-field:focus {
  outline: none;
  border-color: rgba(66, 133, 166, 0.5);
}

/* User attribution display in lists/cards */
.record-attribution {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.record-attribution .created-by,
.record-attribution .updated-by {
  display: block;
  margin-bottom: 0.25rem;
}

.record-attribution .created-by::before {
  content: "Created by: ";
  font-weight: 600;
}

.record-attribution .updated-by::before {
  content: "Last updated by: ";
  font-weight: 600;
}



/* CRITICAL MOBILE OVERRIDE - Must be at end of file */
@media (max-width: 767px) {
  /* Force mobile layout regardless of other styles */
  .animal-cards {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.5rem !important;
    width: 100% !important;
  }

  .controls-container {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
    width: 100% !important;
  }

  .filter-dropdowns {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
    width: 100% !important;
  }

  .filter-dropdown {
    width: 100% !important;
    max-width: none !important;
  }

  .search-bar {
    width: 100% !important;
    max-width: none !important;
  }

  /* Ensure app container fits mobile */
  .app-container {
    width: 98% !important;
    max-width: none !important;
    padding: 0.75rem !important;
  }

  body {
    padding: 0.5rem !important;
  }

  /* Critical mobile animal card fixes */
  .animal-card {
    padding: 0.75rem !important;
    gap: 0.5rem !important;
  }

  .animal-card img {
    width: 70px !important;
    height: 70px !important;
  }

  .animal-card .name {
    font-size: 0.95rem !important;
    margin: 0.25rem 0 !important;
    line-height: 1.1 !important;
  }

  .animal-card .status-badge {
    padding: 0.2rem 0.5rem !important;
    font-size: 0.65rem !important;
    border-radius: 10px !important;
    letter-spacing: 0.2px !important;
  }

  .animal-card .detail-row {
    gap: 0.25rem !important;
  }

  .animal-card .detail-label,
  .animal-card .detail-value {
    font-size: 0.75rem !important;
  }

  .animal-card .details {
    gap: 0.25rem !important;
  }
}

/* Dashboard Styles - Mobile First */
.dashboard-main {
  padding: 1rem 0;
}

.date-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.date-display .material-icons {
  color: #4285a6;
}

/* Alert Panel */
.alert-panel {
  margin-bottom: 1rem;
}

.alert {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  margin-bottom: 0.5rem;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.alert-warning {
  background: rgba(255, 193, 7, 0.2);
  border-color: rgba(255, 193, 7, 0.4);
  color: #856404;
}

.alert-info {
  background: rgba(33, 150, 243, 0.2);
  border-color: rgba(33, 150, 243, 0.4);
  color: #0c5460;
}

.alert-message {
  flex: 1;
  font-size: 0.9rem;
}

.alert-action, .alert-dismiss {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 0.25rem 0.5rem;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.alert-action:hover, .alert-dismiss:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
}

/* Dashboard Grid - Mobile First */
.dashboard-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.dashboard-section {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
}

.section-title .material-icons {
  color: #4285a6;
}

/* Stats Cards - Mobile First (Single Column) */
.stats-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  padding: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: rgba(66, 133, 166, 0.2);
}

.stat-icon .material-icons {
  font-size: 24px;
  color: #4285a6;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #555;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
}

/* Card Color Variations */
.animals-total .stat-icon { background: rgba(76, 175, 80, 0.2); }
.animals-total .stat-icon .material-icons { color: #4CAF50; }

.animals-not-clear .stat-icon { background: rgba(255, 152, 0, 0.2); }
.animals-not-clear .stat-icon .material-icons { color: #FF9800; }

.animals-no-log .stat-icon { background: rgba(244, 67, 54, 0.2); }
.animals-no-log .stat-icon .material-icons { color: #F44336; }

.medical-active .stat-icon { background: rgba(233, 30, 99, 0.2); }
.medical-active .stat-icon .material-icons { color: #E91E63; }

.medical-recovery .stat-icon { background: rgba(33, 150, 243, 0.2); }
.medical-recovery .stat-icon .material-icons { color: #2196F3; }

.adoption-expiring .stat-icon { background: rgba(156, 39, 176, 0.2); }
.adoption-expiring .stat-icon .material-icons { color: #9C27B0; }

.staff-total .stat-icon { background: rgba(0, 150, 136, 0.2); }
.staff-total .stat-icon .material-icons { color: #009688; }

.staff-activity .stat-icon { background: rgba(139, 195, 74, 0.2); }
.staff-activity .stat-icon .material-icons { color: #8BC34A; }

.staff-inactive .stat-icon { background: rgba(121, 85, 72, 0.2); }
.staff-inactive .stat-icon .material-icons { color: #795548; }

.documents-review .stat-icon { background: rgba(255, 193, 7, 0.2); }
.documents-review .stat-icon .material-icons { color: #FFC107; }

/* Chart Container */
.chart-container {
  margin-top: 1rem;
}

.chart-container h3 {
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
}

.chart-container canvas {
  max-height: 300px;
}

/* Extra small mobile devices (max-width: 400px) */
@media (max-width: 400px) {
  .animal-cards {
    gap: 0.4rem !important;
  }

  .animal-card {
    padding: 0.6rem !important;
    gap: 0.4rem !important;
  }

  .animal-card .top-section {
    gap: 0.6rem !important;
  }

  .animal-card img {
    width: 50px !important;
    height: 50px !important;
  }

  .animal-card .name {
    font-size: clamp(0.85rem, 4vw, 0.95rem) !important;
    line-height: 1.1 !important;
  }

  .animal-card .status-badge {
    font-size: 0.6rem !important;
    padding: 0.25rem 0.4rem !important;
    border-radius: 12px !important;
    letter-spacing: 0.1px !important;
    line-height: 1.1 !important;
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  .animal-card .details {
    gap: 0.3rem !important;
  }

  .animal-card .detail-row {
    font-size: clamp(0.65rem, 3vw, 0.7rem) !important;
    border-radius: 15px !important;
  }

  .animal-card img {
    width: 60px !important;
    height: 60px !important;
  }

  .animal-card .name {
    font-size: 0.85rem !important;
    margin: 0.2rem 0 !important;
  }

  .animal-card .status-badge {
    padding: 0.15rem 0.4rem !important;
    font-size: 0.6rem !important;
    border-radius: 8px !important;
  }

  .animal-card .detail-label,
  .animal-card .detail-value {
    font-size: 0.7rem !important;
  }

  .app-container {
    width: 99% !important;
    padding: 0.5rem !important;
  }

  body {
    padding: 0.25rem !important;
  }

  /* Dashboard mobile adjustments */
  .dashboard-section {
    padding: 1rem;
  }

  .stat-card {
    padding: 0.75rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
  }

  .stat-icon .material-icons {
    font-size: 20px;
  }

  .stat-number {
    font-size: 1.5rem;
  }
}

/* Dashboard Tablet Styles (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .dashboard-section {
    padding: 1rem;
    min-height: 0; /* Allow sections to shrink */
  }

  /* First three sections (Animals, Medical & Welfare, Staff & Users) in 3-column layout */
  .dashboard-section:nth-child(1),
  .dashboard-section:nth-child(2),
  .dashboard-section:nth-child(3) {
    grid-column: span 1;
  }

  /* Adoptions & Donations section spans full width */
  .dashboard-section-full {
    grid-column: span 3;
  }

  /* Document Compliance section spans full width */
  .dashboard-section:last-child {
    grid-column: span 3;
  }

  /* Stats cards within each section - single column for better fit */
  .stats-cards {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  /* Document Compliance stats in 4-column layout on tablet */
  .dashboard-section:last-child .stats-cards {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }

  /* Adoptions & Donations stats in 2-column layout */
  .dashboard-section-full .stats-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  /* Much smaller cards and content for tablet */
  .stat-card {
    padding: 0.75rem;
    min-height: 0;
  }

  .stat-icon {
    width: 35px;
    height: 35px;
    margin-bottom: 0.5rem;
  }

  .stat-icon .material-icons {
    font-size: 18px;
  }

  .stat-number {
    font-size: 1.4rem;
    margin-bottom: 0.25rem;
  }

  .stat-content h3 {
    font-size: 0.75rem;
    line-height: 1.1;
    margin-bottom: 0;
  }

  .section-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .section-title .material-icons {
    font-size: 18px;
  }

  .chart-container canvas {
    max-height: 250px;
  }
}

/* Dashboard Desktop Styles (1024px+) */
@media (min-width: 1024px) {
  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
  }

  .dashboard-section {
    padding: 2rem;
  }

  /* First three sections (Animals, Medical & Welfare, Staff & Users) in 3-column layout */
  .dashboard-section:nth-child(1),
  .dashboard-section:nth-child(2),
  .dashboard-section:nth-child(3) {
    grid-column: span 1;
  }

  /* Adoptions & Donations section spans full width */
  .dashboard-section-full {
    grid-column: span 3;
  }

  /* Document Compliance section spans full width */
  .dashboard-section:last-child {
    grid-column: span 3;
  }

  /* Stats cards within each main section - single column */
  .stats-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  /* Document Compliance stats in 4-column layout on desktop */
  .dashboard-section:last-child .stats-cards {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  /* Adoptions & Donations stats in 2-column layout on desktop */
  .dashboard-section-full .stats-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .stat-icon {
    width: 60px;
    height: 60px;
  }

  .stat-icon .material-icons {
    font-size: 28px;
  }

  .stat-content h3 {
    font-size: 1rem;
  }

  .stat-number {
    font-size: 2.2rem;
  }

  .chart-container canvas {
    max-height: 400px;
  }
}

/* Clickable Dashboard Cards */
.clickable-card {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.clickable-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.clickable-card:active {
  transform: translateY(0);
}

/* Medium-Large Tablet/Small Desktop Adjustments (900px - 1255px) - Fix overflow issues */
@media (min-width: 900px) and (max-width: 1255px) {
  .dashboard-grid {
    gap: 1.5rem;
  }

  .dashboard-section {
    padding: 1.25rem;
    min-height: 0;
  }

  /* Ensure cards fit properly in 3-column layout */
  .stats-cards {
    gap: 0.875rem;
  }

  .stat-card {
    padding: 1rem;
    min-height: 0;
  }

  .stat-icon {
    width: 42px;
    height: 42px;
    margin-bottom: 0.5rem;
  }

  .stat-icon .material-icons {
    font-size: 20px;
  }

  .stat-number {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
  }

  .stat-content h3 {
    font-size: 0.8rem;
    line-height: 1.2;
  }

  .section-title {
    font-size: 1.05rem;
    margin-bottom: 0.75rem;
  }

  .section-title .material-icons {
    font-size: 19px;
  }

  /* Adoptions & Donations chart adjustment */
  .chart-container canvas {
    max-height: 280px;
  }
}

@media (max-width: 767px) {
  .dashboard-section-full {
    grid-column: span 1;
  }

  /* Document Compliance section single column on mobile */
  .dashboard-section:last-child {
    grid-column: span 1;
  }

  /* Document Compliance stats back to single column on mobile */
  .dashboard-section:last-child .stats-cards {
    grid-template-columns: 1fr !important;
    gap: 1rem;
  }
}

/* FAB Styles for Dashboard */
.fab-container {
  position: fixed;
  bottom: 80px; /* Position above GDPR footer */
  right: 2rem;
  z-index: 1000;
}

.fab-main {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: #4285a6;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab-main:hover {
  background: #3a7594;
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.fab-main .material-icons {
  color: white;
  font-size: 24px;
}

.fab-actions {
  position: absolute;
  bottom: 70px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.fab-actions.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.fab-action {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 28px;
  padding: 0.75rem 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  text-decoration: none;
  color: #2c3e50;
}

.fab-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 1);
}

.fab-action .material-icons {
  color: #4285a6;
  font-size: 20px;
}

.fab-action .label {
  font-size: 0.9rem;
  font-weight: 600;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .user-attribution {
    padding: 0.5rem;
    margin: 0.75rem 0;
  }

  .user-attribution label {
    font-size: 0.85rem;
  }

  .user-attribution .readonly-field {
    font-size: 0.85rem;
    padding: 0.4rem;
  }

  .record-attribution {
    font-size: 0.75rem;
  }
}
